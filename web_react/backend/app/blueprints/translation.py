"""
Translation Blueprint for TCF Canada

This blueprint provides translation services using Azure Translator API.
Supports French to English and French to Chinese translations for the French practice website.
"""

from flask import Blueprint, request, jsonify, current_app
import requests
import json
import uuid
from functools import wraps
from openai import OpenAI
import time

# Create the blueprint
translation_bp = Blueprint('translation', __name__, url_prefix='/api/translation')

def require_translation_config(f):
    """Decorator to ensure translation service configuration is available"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_app.config.get('OPENAI_API_KEY') and not current_app.config.get('AZURE_TRANSLATOR_KEY'):
            return jsonify({
                'error': 'Translation service not configured',
                'message': 'Neither OpenAI nor Azure Translator API key is available'
            }), 500
        return f(*args, **kwargs)
    return decorated_function

def validate_translation_request(data):
    """Validate translation request data"""
    if not data:
        return False, "No data provided"
    
    text = data.get('text', '').strip()
    target_language = data.get('target_language', '').strip()
    
    if not text:
        return False, "Text to translate is required"
    
    if not target_language:
        return False, "Target language is required"
    
    # Supported target languages for French practice website
    supported_languages = ['en', 'zh']  # English and Chinese
    if target_language not in supported_languages:
        return False, f"Unsupported target language. Supported languages: {', '.join(supported_languages)}"
    
    # Check text length (Azure Translator has limits)
    if len(text) > 10000:  # 10KB limit
        return False, "Text is too long. Maximum 10,000 characters allowed"
    
    return True, None

# Budget tracking variables
translation_cost = 0.0
translation_requests = 0

# Request deduplication cache
import hashlib
import threading
from datetime import datetime, timedelta
request_cache = {}
active_requests = {}  # Track ongoing requests to prevent race conditions
cache_lock = threading.Lock()
CACHE_DURATION_SECONDS = 10  # Cache identical requests for 10 seconds

def get_translation_cost():
    """Get current translation cost for this session"""
    return translation_cost

def add_translation_cost(cost):
    """Add cost to the translation budget tracking"""
    global translation_cost, translation_requests
    translation_cost += cost
    translation_requests += 1
    # Removed verbose cost logging

def can_afford_translation(estimated_cost=0.0008):
    """Check if we can afford another translation based on budget"""
    budget_limit = float(current_app.config.get('OPENAI_TRANSLATION_BUDGET', '3.0'))
    return (translation_cost + estimated_cost) <= budget_limit

def call_openai_translator(text, target_language, source_language='fr'):
    """Call OpenAI GPT-4o Mini for translation"""
    try:
        # Check budget first
        if not can_afford_translation():
            current_app.logger.warning("OpenAI translation budget exceeded, falling back to Azure")
            return {'success': False, 'error': 'Budget exceeded', 'fallback_needed': True}

        # Set up OpenAI client with new API
        client = OpenAI(api_key=current_app.config['OPENAI_API_KEY'])
        
        # Language mappings for better prompts
        language_names = {
            'fr': 'French',
            'en': 'English', 
            'zh': 'Chinese'
        }
        
        source_name = language_names.get(source_language, source_language)
        target_name = language_names.get(target_language, target_language)
        
        # Create translation prompt
        system_message = f"You are a professional translator. Translate from {source_name} to {target_name} accurately and naturally. Provide only the translation, no explanations or additional text."
        user_message = f"Translate this {source_name} text to {target_name}: {text}"

        # Make OpenAI API call
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": user_message}
            ],
            max_tokens=1000,
            temperature=0.1
        )
        
        # Calculate cost (approximate)
        prompt_tokens = response.usage.prompt_tokens
        completion_tokens = response.usage.completion_tokens
        # GPT-4o Mini pricing: $0.00015/1K prompt tokens, $0.0006/1K completion tokens
        cost = (prompt_tokens * 0.00015 / 1000) + (completion_tokens * 0.0006 / 1000)
        add_translation_cost(cost)
        
        translated_text = response.choices[0].message.content.strip()
        
        # Removed verbose logging - will be handled by caller
        
        return {
            'success': True,
            'translated_text': translated_text,
            'source_language': source_language,
            'target_language': target_language,
            'confidence': 1.0,  # GPT doesn't provide confidence, assume high
            'service': 'openai',
            'cost': cost
        }
        
    except Exception as e:
        current_app.logger.error(f"OpenAI translation failed: {str(e)}")
        return {
            'success': False,
            'error': f'OpenAI translation failed: {str(e)}',
            'fallback_needed': True
        }

def call_azure_translator(text, target_language, source_language='fr'):
    """Call Azure Translator API"""
    try:
        # Azure Translator API configuration
        key = current_app.config['AZURE_TRANSLATOR_KEY']
        endpoint = current_app.config['AZURE_TRANSLATOR_ENDPOINT']
        region = current_app.config['AZURE_TRANSLATOR_REGION']
        
        # Construct the request
        path = '/translate'
        constructed_url = endpoint + path
        
        params = {
            'api-version': '3.0',
            'from': source_language,
            'to': target_language
        }
        
        headers = {
            'Ocp-Apim-Subscription-Key': key,
            'Ocp-Apim-Subscription-Region': region,
            'Content-type': 'application/json',
            'X-ClientTraceId': str(uuid.uuid4())
        }
        
        # Request body
        body = [{
            'text': text
        }]
        
        # Make the request
        response = requests.post(constructed_url, params=params, headers=headers, json=body)
        response.raise_for_status()
        
        # Parse response
        result = response.json()
        
        if result and len(result) > 0 and 'translations' in result[0]:
            translated_text = result[0]['translations'][0]['text']
            detected_language = result[0].get('detectedLanguage', {}).get('language', source_language)
            confidence = result[0].get('detectedLanguage', {}).get('score', 1.0)
            
            return {
                'success': True,
                'translated_text': translated_text,
                'source_language': detected_language,
                'target_language': target_language,
                'confidence': confidence,
                'service': 'azure'
            }
        else:
            return {
                'success': False,
                'error': 'Invalid response from translation service'
            }
            
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"Azure Translator API request failed: {str(e)}")
        return {
            'success': False,
            'error': f'Translation service request failed: {str(e)}'
        }
    except Exception as e:
        current_app.logger.error(f"Translation error: {str(e)}")
        return {
            'success': False,
            'error': f'Translation failed: {str(e)}'
        }

def translate_with_fallback(text, target_language, source_language='fr'):
    """
    Main translation function that tries OpenAI first, then falls back to Azure
    """
    import uuid
    request_id = str(uuid.uuid4())[:8]
    
    # Create cache key for deduplication
    cache_key = hashlib.md5(f"{text}|{target_language}|{source_language}".encode()).hexdigest()
    
    with cache_lock:
        now = datetime.now()
        
        # Clean up expired cache entries (prevent memory leaks)
        expired_keys = [k for k, (_, time) in request_cache.items() 
                       if now - time >= timedelta(seconds=CACHE_DURATION_SECONDS)]
        for k in expired_keys:
            del request_cache[k]
        
        # Check if we have a cached result
        if cache_key in request_cache:
            cached_result, cached_time = request_cache[cache_key]
            if now - cached_time < timedelta(seconds=CACHE_DURATION_SECONDS):
                current_app.logger.info(f"[{request_id}] Returning cached translation (deduplicated)")
                return cached_result
        
        # Check if this exact request is already being processed
        if cache_key in active_requests:
            current_app.logger.info(f"[{request_id}] Request already in progress, waiting...")
            # Wait for the active request to complete
            active_request_event = active_requests[cache_key]
    
    # If there was an active request, wait for it outside the lock
    if cache_key in active_requests:
        active_request_event.wait(timeout=30)  # Wait max 30 seconds
        # After waiting, check cache again
        with cache_lock:
            if cache_key in request_cache:
                cached_result, cached_time = request_cache[cache_key]
                if now - cached_time < timedelta(seconds=CACHE_DURATION_SECONDS):
                    current_app.logger.info(f"[{request_id}] Returning result from completed request (deduplicated)")
                    return cached_result
    
    # Mark this request as active
    with cache_lock:
        if cache_key not in active_requests:
            active_requests[cache_key] = threading.Event()
    
    # Check if OpenAI API key is available
    openai_key = current_app.config.get('OPENAI_API_KEY')
    azure_key = current_app.config.get('AZURE_TRANSLATOR_KEY')
    
    current_app.logger.info(f"[{request_id}] Translation: '{text[:30]}...' -> {target_language}")
    
    # Try OpenAI first if API key is available
    if openai_key:
        openai_result = call_openai_translator(text, target_language, source_language)
        
        if openai_result['success']:
            current_app.logger.info(f"[{request_id}] ✓ GPT-4o Mini success (${openai_result.get('cost', 0):.4f})")
            # Cache successful result and notify waiting threads
            with cache_lock:
                request_cache[cache_key] = (openai_result, now)
                if cache_key in active_requests:
                    active_requests[cache_key].set()
                    del active_requests[cache_key]
            return openai_result
        elif openai_result.get('fallback_needed'):
            current_app.logger.warning(f"[{request_id}] OpenAI failed, trying Azure")
        else:
            current_app.logger.error(f"[{request_id}] OpenAI failed: {openai_result.get('error')}")
            # Clean up active request on failure
            with cache_lock:
                if cache_key in active_requests:
                    active_requests[cache_key].set()
                    del active_requests[cache_key]
            return openai_result
    
    # Fallback to Azure if OpenAI failed or is not available
    if azure_key:
        azure_result = call_azure_translator(text, target_language, source_language)
        
        if azure_result['success']:
            # Add fallback indicator
            azure_result['fallback_used'] = True
            azure_result['primary_service'] = 'openai' if openai_key else 'azure'
            service_name = 'Azure (fallback)' if openai_key else 'Azure'
            current_app.logger.info(f"[{request_id}] ✓ {service_name} success")
            # Cache successful result and notify waiting threads
            with cache_lock:
                request_cache[cache_key] = (azure_result, now)
                if cache_key in active_requests:
                    active_requests[cache_key].set()
                    del active_requests[cache_key]
        else:
            current_app.logger.error(f"[{request_id}] Azure failed: {azure_result.get('error', 'Unknown error')}")
            # Clean up active request on failure
            with cache_lock:
                if cache_key in active_requests:
                    active_requests[cache_key].set()
                    del active_requests[cache_key]
        
        return azure_result
    
    # No translation service available - clean up active request
    with cache_lock:
        if cache_key in active_requests:
            active_requests[cache_key].set()
            del active_requests[cache_key]
    
    return {
        'success': False,
        'error': 'No translation service available'
    }

@translation_bp.route('/translate', methods=['POST'])
@require_translation_config
def translate_text():
    """
    Translate text from French to English or Chinese

    Expected JSON payload:
    {
        "text": "Bonjour le monde",
        "target_language": "en",  // or "zh"
        "method": "gpt4" // optional: "gpt4" or "azure", defaults to user preference
    }

    Returns:
    {
        "success": true,
        "translated_text": "Hello world",
        "source_language": "fr",
        "target_language": "en",
        "confidence": 0.99,
        "method_used": "gpt4"
    }
    """
    try:
        data = request.get_json()

        # Validate request
        is_valid, error_message = validate_translation_request(data)
        if not is_valid:
            return jsonify({
                'success': False,
                'error': error_message
            }), 400

        text = data['text']
        target_language = data['target_language']
        source_language = data.get('source_language', 'fr')  # Default to French
        preferred_method = data.get('method')  # Optional method override

        # Get user preference if no method specified and user is logged in
        if not preferred_method and 'user_id' in session:
            try:
                result = current_app.supabase.table('users').select('profile_data').eq('id', session['user_id']).execute()
                if result.data:
                    user = result.data[0]
                    if user.get('profile_data'):
                        import json
                        profile_data = json.loads(user['profile_data'])
                        preferred_method = profile_data.get('translation_method', 'gpt4')
                    else:
                        preferred_method = 'gpt4'  # Default to GPT-4
                else:
                    preferred_method = 'gpt4'
            except Exception as e:
                print(f"Error getting user preference: {e}")
                preferred_method = 'gpt4'
        elif not preferred_method:
            preferred_method = 'gpt4'  # Default for non-logged-in users

        # Call appropriate translation service based on preference
        if preferred_method == 'azure':
            result = call_azure_translator(text, target_language, source_language)
            if not result['success']:
                # Fallback to GPT-4 if Azure fails
                result = call_openai_translator(text, target_language, source_language)
                result['method_used'] = 'gpt4_fallback'
            else:
                result['method_used'] = 'azure'
        else:  # Default to GPT-4
            result = call_openai_translator(text, target_language, source_language)
            if not result['success'] or result.get('fallback_needed'):
                # Fallback to Azure if GPT-4 fails or budget exceeded
                result = call_azure_translator(text, target_language, source_language)
                result['method_used'] = 'azure_fallback'
            else:
                result['method_used'] = 'gpt4'

        if result['success']:
            return jsonify(result), 200
        else:
            return jsonify(result), 500

    except Exception as e:
        current_app.logger.error(f"Translation endpoint error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@translation_bp.route('/batch-translate', methods=['POST'])
@require_translation_config
def batch_translate():
    """
    Translate multiple texts in a single request
    
    Expected JSON payload:
    {
        "texts": ["Bonjour", "Au revoir", "Merci"],
        "target_language": "en"
    }
    
    Returns:
    {
        "success": true,
        "translations": [
            {
                "original": "Bonjour",
                "translated": "Hello",
                "success": true
            },
            {
                "original": "Au revoir", 
                "translated": "Goodbye",
                "success": true
            },
            {
                "original": "Merci",
                "translated": "Thank you",
                "success": true
            }
        ]
    }
    """
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        texts = data.get('texts', [])
        target_language = data.get('target_language', '').strip()
        
        if not texts or not isinstance(texts, list):
            return jsonify({
                'success': False,
                'error': 'Texts array is required'
            }), 400
        
        if not target_language:
            return jsonify({
                'success': False,
                'error': 'Target language is required'
            }), 400
        
        # Validate target language
        supported_languages = ['en', 'zh']
        if target_language not in supported_languages:
            return jsonify({
                'success': False,
                'error': f'Unsupported target language. Supported languages: {", ".join(supported_languages)}'
            }), 400
        
        # Limit batch size
        if len(texts) > 100:
            return jsonify({
                'success': False,
                'error': 'Too many texts. Maximum 100 texts per batch'
            }), 400
        
        # Process translations
        translations = []
        source_language = data.get('source_language', 'fr')
        
        for text in texts:
            if not text or not isinstance(text, str):
                translations.append({
                    'original': text,
                    'translated': '',
                    'success': False,
                    'error': 'Invalid text'
                })
                continue
            
            text = text.strip()
            if not text:
                translations.append({
                    'original': text,
                    'translated': '',
                    'success': False,
                    'error': 'Empty text'
                })
                continue
            
            # Translate individual text
            result = translate_with_fallback(text, target_language, source_language)
            
            if result['success']:
                translations.append({
                    'original': text,
                    'translated': result['translated_text'],
                    'success': True
                })
            else:
                translations.append({
                    'original': text,
                    'translated': '',
                    'success': False,
                    'error': result.get('error', 'Translation failed')
                })
        
        return jsonify({
            'success': True,
            'translations': translations,
            'target_language': target_language,
            'source_language': source_language
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Batch translation endpoint error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@translation_bp.route('/supported-languages', methods=['GET'])
def get_supported_languages():
    """
    Get list of supported languages for translation
    
    Returns:
    {
        "success": true,
        "languages": {
            "source": ["fr"],
            "target": ["en", "zh"]
        },
        "language_names": {
            "fr": "Français",
            "en": "English", 
            "zh": "中文"
        }
    }
    """
    return jsonify({
        'success': True,
        'languages': {
            'source': ['fr'],  # French practice website - source is always French
            'target': ['en', 'zh']  # English and Chinese
        },
        'language_names': {
            'fr': 'Français',
            'en': 'English',
            'zh': '中文'
        }
    }), 200

@translation_bp.route('/health', methods=['GET'])
@require_translation_config
def translation_health():
    """
    Health check for translation service
    
    Returns:
    {
        "success": true,
        "primary_service": "OpenAI GPT-3.5",
        "fallback_service": "Azure Translator", 
        "status": "healthy",
        "configured": true,
        "budget_info": {...}
    }
    """
    try:
        # Test with a simple translation
        test_result = translate_with_fallback("Bonjour", "en", "fr")
        
        # Get service status
        openai_configured = bool(current_app.config.get('OPENAI_API_KEY'))
        azure_configured = bool(current_app.config.get('AZURE_TRANSLATOR_KEY'))
        
        budget_limit = float(current_app.config.get('OPENAI_TRANSLATION_BUDGET', '3.0'))
        
        if test_result['success']:
            return jsonify({
                'success': True,
                'primary_service': 'OpenAI GPT-3.5' if openai_configured else 'Azure Translator',
                'fallback_service': 'Azure Translator' if openai_configured else None,
                'status': 'healthy',
                'configured': True,
                'service_used': test_result.get('service', 'unknown'),
                'fallback_used': test_result.get('fallback_used', False),
                'budget_info': {
                    'budget_limit': budget_limit,
                    'current_cost': translation_cost,
                    'remaining_budget': budget_limit - translation_cost,
                    'requests_made': translation_requests
                }
            }), 200
        else:
            return jsonify({
                'success': False,
                'primary_service': 'OpenAI GPT-3.5' if openai_configured else 'Azure Translator',
                'fallback_service': 'Azure Translator' if openai_configured else None,
                'status': 'unhealthy',
                'configured': True,
                'error': test_result.get('error', 'Service test failed'),
                'budget_info': {
                    'budget_limit': budget_limit,
                    'current_cost': translation_cost,
                    'remaining_budget': budget_limit - translation_cost,
                    'requests_made': translation_requests
                }
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'primary_service': 'OpenAI GPT-3.5' if current_app.config.get('OPENAI_API_KEY') else 'Azure Translator',
            'fallback_service': 'Azure Translator' if current_app.config.get('OPENAI_API_KEY') else None,
            'status': 'error',
            'configured': True,
            'error': str(e)
        }), 500

@translation_bp.route('/budget-status', methods=['GET'])
def budget_status():
    """
    Get current translation budget status
    
    Returns:
    {
        "budget_limit": 3.0,
        "current_cost": 0.15,
        "remaining_budget": 2.85,
        "requests_made": 42,
        "budget_percentage_used": 5.0
    }
    """
    try:
        budget_limit = float(current_app.config.get('OPENAI_TRANSLATION_BUDGET', '3.0'))
        percentage_used = (translation_cost / budget_limit * 100) if budget_limit > 0 else 0
        
        return jsonify({
            'budget_limit': budget_limit,
            'current_cost': round(translation_cost, 4),
            'remaining_budget': round(budget_limit - translation_cost, 4),
            'requests_made': translation_requests,
            'budget_percentage_used': round(percentage_used, 2),
            'can_make_request': can_afford_translation()
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Budget status error: {str(e)}")
        return jsonify({
            'error': 'Failed to get budget status',
            'message': str(e)
        }), 500
