#!/usr/bin/env python3
"""
Test script to verify both GPT-4 and Azure translation services are working
"""

import requests
import json

# Test data
test_text = "Bonjour, comment allez-vous?"
base_url = "http://localhost:5001/api/translation"

def test_translation_method(method, text, target_language="en"):
    """Test a specific translation method"""
    print(f"\n🧪 Testing {method.upper()} translation...")
    
    payload = {
        "text": text,
        "target_language": target_language,
        "method": method
    }
    
    try:
        response = requests.post(f"{base_url}/translate", json=payload)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ {method.upper()} Success:")
                print(f"   Original: {text}")
                print(f"   Translated: {result.get('translated_text')}")
                print(f"   Method used: {result.get('method_used')}")
                print(f"   Confidence: {result.get('confidence', 'N/A')}")
                return True
            else:
                print(f"❌ {method.upper()} Failed: {result.get('error')}")
                return False
        else:
            print(f"❌ {method.upper()} HTTP Error {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ {method.upper()} Connection Error: Server not running?")
        return False
    except Exception as e:
        print(f"❌ {method.upper()} Error: {str(e)}")
        return False

def test_service_status():
    """Test the service status endpoint"""
    print("\n🔍 Checking service status...")
    
    try:
        response = requests.get(f"{base_url}/status")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Service Status:")
            print(f"   OpenAI configured: {result.get('openai_configured', False)}")
            print(f"   Azure configured: {result.get('azure_configured', False)}")
            print(f"   Test result: {result.get('test_result', {}).get('success', False)}")
            return True
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Status check error: {str(e)}")
        return False

def main():
    print("🚀 Testing Translation APIs")
    print("=" * 50)
    
    # Test service status first
    status_ok = test_service_status()
    
    # Test both methods
    gpt4_ok = test_translation_method("gpt4", test_text)
    azure_ok = test_translation_method("azure", test_text)
    
    # Test Chinese translation
    chinese_ok = test_translation_method("gpt4", test_text, "zh")
    
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"   Service Status: {'✅' if status_ok else '❌'}")
    print(f"   GPT-4 Translation: {'✅' if gpt4_ok else '❌'}")
    print(f"   Azure Translation: {'✅' if azure_ok else '❌'}")
    print(f"   Chinese Translation: {'✅' if chinese_ok else '❌'}")
    
    if gpt4_ok and azure_ok:
        print("\n🎉 All translation services are working!")
    else:
        print("\n⚠️  Some translation services have issues.")

if __name__ == "__main__":
    main()
